import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import WebView from 'react-native-webview';
import { useAuth } from '../../contexts/AuthContext';

export default function HomeScreen() {
  const { isAuthenticated, token, isLoading } = useAuth();
  const [webViewUrl, setWebViewUrl] = useState('https://piattaforma.controllone.it');

  // Check authentication and redirect if not logged in
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/splash1');
    }
  }, [isAuthenticated, isLoading]);

  // Update WebView URL with token when available
  useEffect(() => {
    if (token) {
      const baseUrl = 'https://piattaforma.controllone.it';
      const urlWithToken = `${baseUrl}?token=${encodeURIComponent(token)}`;
      setWebViewUrl(urlWithToken);
    }
  }, [token]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loader}>
          <ActivityIndicator size="large" color="#F54619" />
        </View>
      </View>
    );
  }

  // Don't render WebView if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <View style={styles.container}>
      <WebView
        source={{ uri: webViewUrl }}
        style={styles.webview}
        startInLoadingState={true}
        renderLoading={() => (
          <View style={styles.loader}>
            <ActivityIndicator size="large" color="#F54619" />
          </View>
        )}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('WebView error: ', nativeEvent);
        }}
        onHttpError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('WebView HTTP error: ', nativeEvent);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingTop: 30,
    paddingBottom: 30,
  },
  webview: {
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
  },
  loader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    zIndex: 10,
  }
});
