/**
 * API Configuration for ControllOne
 */

// Base URL for the API
export const BASE_URL = 'https://piattaforma.controllone.it';

// API Endpoints
export const API_ENDPOINTS = {
  LOGIN: '/api/login',
  FORGOT_PASSWORD: '/api/forgot-password',
} as const;

// HTTP Methods
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
} as const;

// Response status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// API Response types
export interface ApiResponse<T = any> {
  message: string;
  data?: T;
  token?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  language: string;
}

export interface LoginResponse {
  message: string;
  token: string;
}

export interface ForgotPasswordRequest {
  email: string;
  language: string;
}

export interface ForgotPasswordResponse {
  message: string;
}

// API Error type
export interface ApiError {
  message: string;
  status?: number;
}
