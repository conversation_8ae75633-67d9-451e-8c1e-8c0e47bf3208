import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Storage utility for managing authentication tokens and user data
 * Uses SecureStore for sensitive data like tokens and AsyncStorage for non-sensitive data
 * Falls back to localStorage on web platform where native storage is not available
 */

// Storage keys
const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  LANGUAGE: 'user_language',
} as const;

// Check if SecureStore is available (not on web)
const isSecureStoreAvailable = Platform.OS !== 'web';

// Web storage fallback
const webStorage = {
  async setItem(key: string, value: string): Promise<void> {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.setItem(key, value);
    }
  },

  async getItem(key: string): Promise<string | null> {
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage.getItem(key);
    }
    return null;
  },

  async removeItem(key: string): Promise<void> {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.removeItem(key);
    }
  },
};

/**
 * Secure storage functions for sensitive data
 */
export const secureStorage = {
  /**
   * Store authentication token securely
   */
  async setToken(token: string): Promise<void> {
    try {
      if (isSecureStoreAvailable) {
        await SecureStore.setItemAsync(STORAGE_KEYS.AUTH_TOKEN, token);
      } else if (Platform.OS === 'web') {
        // Fallback to localStorage on web
        await webStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      } else {
        // Fallback to AsyncStorage on other platforms
        await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      }
    } catch (error) {
      console.error('Error storing token:', error);
      throw new Error('Failed to store authentication token');
    }
  },

  /**
   * Retrieve authentication token
   */
  async getToken(): Promise<string | null> {
    try {
      if (isSecureStoreAvailable) {
        return await SecureStore.getItemAsync(STORAGE_KEYS.AUTH_TOKEN);
      } else if (Platform.OS === 'web') {
        // Fallback to localStorage on web
        return await webStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      } else {
        // Fallback to AsyncStorage on other platforms
        return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      }
    } catch (error) {
      console.error('Error retrieving token:', error);
      return null;
    }
  },

  /**
   * Remove authentication token
   */
  async removeToken(): Promise<void> {
    try {
      if (isSecureStoreAvailable) {
        await SecureStore.deleteItemAsync(STORAGE_KEYS.AUTH_TOKEN);
      } else if (Platform.OS === 'web') {
        // Fallback to localStorage on web
        await webStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      } else {
        // Fallback to AsyncStorage on other platforms
        await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      }
    } catch (error) {
      console.error('Error removing token:', error);
      throw new Error('Failed to remove authentication token');
    }
  },

  /**
   * Check if user is authenticated (has valid token)
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await this.getToken();
      return token !== null && token.length > 0;
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  },
};

/**
 * Regular storage functions for non-sensitive data
 */
export const storage = {
  /**
   * Store user data
   */
  async setUserData(userData: any): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await webStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      } else {
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  },

  /**
   * Retrieve user data
   */
  async getUserData(): Promise<any | null> {
    try {
      let userData: string | null;
      if (Platform.OS === 'web') {
        userData = await webStorage.getItem(STORAGE_KEYS.USER_DATA);
      } else {
        userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      }
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  },

  /**
   * Remove user data
   */
  async removeUserData(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await webStorage.removeItem(STORAGE_KEYS.USER_DATA);
      } else {
        await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
      }
    } catch (error) {
      console.error('Error removing user data:', error);
      throw new Error('Failed to remove user data');
    }
  },

  /**
   * Store user language preference
   */
  async setLanguage(language: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await webStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
      } else {
        await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
      }
    } catch (error) {
      console.error('Error storing language:', error);
      throw new Error('Failed to store language preference');
    }
  },

  /**
   * Retrieve user language preference
   */
  async getLanguage(): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        return await webStorage.getItem(STORAGE_KEYS.LANGUAGE);
      } else {
        return await AsyncStorage.getItem(STORAGE_KEYS.LANGUAGE);
      }
    } catch (error) {
      console.error('Error retrieving language:', error);
      return null;
    }
  },

  /**
   * Clear all stored data
   */
  async clearAll(): Promise<void> {
    try {
      await Promise.all([
        secureStorage.removeToken(),
        this.removeUserData(),
        AsyncStorage.removeItem(STORAGE_KEYS.LANGUAGE),
      ]);
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw new Error('Failed to clear storage');
    }
  },
};
