import { 
  BASE_URL, 
  API_ENDPOINTS, 
  HTTP_METHODS, 
  HTTP_STATUS,
  LoginRequest,
  LoginResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ApiError 
} from '../constants/Api';

/**
 * Authentication service for handling API calls
 */
class AuthService {
  /**
   * Make API request with error handling
   */
  private async makeRequest<T>(
    endpoint: string,
    method: string = HTTP_METHODS.GET,
    body?: any
  ): Promise<T> {
    try {
      const url = `${BASE_URL}${endpoint}`;
      
      const config: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      };

      if (body && method !== HTTP_METHODS.GET) {
        config.body = JSON.stringify(body);
      }

      console.log(`Making ${method} request to: ${url}`);
      console.log('Request body:', body);

      const response = await fetch(url, config);
      
      console.log('Response status:', response.status);
      
      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (response.status !== HTTP_STATUS.OK) {
        const error: ApiError = {
          message: responseData.message || 'An error occurred',
          status: response.status,
        };
        throw error;
      }

      return responseData as T;
    } catch (error) {
      console.error('API request error:', error);
      
      if (error instanceof Error) {
        // Network or parsing error
        const apiError: ApiError = {
          message: error.message || 'Network error occurred',
        };
        throw apiError;
      }
      
      // Re-throw API errors
      throw error;
    }
  }

  /**
   * Login user with email and password
   */
  async login(email: string, password: string, language: string): Promise<LoginResponse> {
    const loginData: LoginRequest = {
      email: email.trim().toLowerCase(),
      password,
      language,
    };

    return this.makeRequest<LoginResponse>(
      API_ENDPOINTS.LOGIN,
      HTTP_METHODS.POST,
      loginData
    );
  }

  /**
   * Send forgot password request
   */
  async forgotPassword(email: string, language: string): Promise<ForgotPasswordResponse> {
    const forgotPasswordData: ForgotPasswordRequest = {
      email: email.trim().toLowerCase(),
      language,
    };

    return this.makeRequest<ForgotPasswordResponse>(
      API_ENDPOINTS.FORGOT_PASSWORD,
      HTTP_METHODS.POST,
      forgotPasswordData
    );
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  /**
   * Validate password (minimum requirements)
   */
  validatePassword(password: string): boolean {
    return password.length >= 6; // Minimum 6 characters
  }

  /**
   * Validate login form
   */
  validateLoginForm(email: string, password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!email.trim()) {
      errors.push('Email is required');
    } else if (!this.validateEmail(email)) {
      errors.push('Please enter a valid email address');
    }

    if (!password.trim()) {
      errors.push('Password is required');
    } else if (!this.validatePassword(password)) {
      errors.push('Password must be at least 6 characters long');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate forgot password form
   */
  validateForgotPasswordForm(email: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!email.trim()) {
      errors.push('Email is required');
    } else if (!this.validateEmail(email)) {
      errors.push('Please enter a valid email address');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const authService = new AuthService();
